<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات الزيارات - معهد دارين</title>
    <meta name="description" content="إحصائيات الزيارات في معهد دارين - أفضل مدرسة افتراضية">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts - Tajawal (Arabic) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/animations.css">
    <link rel="stylesheet" href="../assets/css/notifications.css">
    <link rel="stylesheet" href="dashboard-modern.css">
    <link rel="stylesheet" href="dashboard-modern-utils.css">
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }

        .period-selector {
            display: flex;
            justify-content: center;
            margin-bottom: 1.5rem;
            gap: 1rem;
        }

        .period-btn {
            padding: 0.5rem 1.5rem;
            border-radius: 50px;
            background-color: var(--white);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .period-btn:hover {
            background-color: rgba(0, 137, 123, 0.05);
            transform: translateY(-2px);
        }

        .period-btn.active {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background-color: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
            text-align: center;
        }

        .summary-card h3 {
            color: var(--text-muted);
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .summary-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .summary-change {
            display: inline-flex;
            align-items: center;
            font-size: 0.9rem;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
        }

        .summary-change.positive {
            background-color: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }

        .summary-change.negative {
            background-color: rgba(244, 67, 54, 0.1);
            color: #f44336;
        }

        .summary-change.neutral {
            background-color: rgba(158, 158, 158, 0.1);
            color: #9e9e9e;
        }

        .summary-change i {
            margin-left: 0.25rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html">
                        <h1>معهد دارين</h1>
                    </a>
                </div>

                <div class="admin-profile">
                    <div class="admin-avatar">د</div>
                    <div class="admin-info">
                        <div class="admin-name">دارين</div>
                        <div class="admin-role">مدير النظام</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Dashboard Section -->
    <section class="dashboard-section">
        <div class="container">
            <div class="dashboard-container">
                <!-- Sidebar -->
                <aside class="dashboard-sidebar">
                    <div class="sidebar-header">
                        <h3>لوحة التحكم</h3>
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <nav class="sidebar-nav">
                        <ul>
                            <li>
                                <a href="dashboard-new.html">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span>الرئيسية</span>
                                </a>
                            </li>
                            <li>
                                <a href="note-requests.html">
                                    <i class="fas fa-book"></i>
                                    <span>طلبات المذكرات</span>
                                    <span class="badge" id="requests-count">3</span>
                                </a>
                            </li>
                            <li>
                                <a href="users.html">
                                    <i class="fas fa-users"></i>
                                    <span>المستخدمين</span>
                                </a>
                            </li>
                            <li>
                                <a href="courses.html">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>الدورات</span>
                                </a>
                            </li>
                            <li class="active">
                                <a href="visits.html">
                                    <i class="fas fa-chart-line"></i>
                                    <span>إحصائيات الزيارات</span>
                                </a>
                            </li>
                            <li>
                                <a href="settings.html">
                                    <i class="fas fa-cog"></i>
                                    <span>الإعدادات</span>
                                </a>
                            </li>
                            <li>
                                <a href="../pages/login.html" id="logout-btn">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>تسجيل الخروج</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </aside>

                <!-- Main Content -->
                <main class="dashboard-content">
                    <div class="dashboard-header">
                        <h2>إحصائيات الزيارات</h2>
                        <div class="date-time">
                            <i class="far fa-calendar-alt"></i>
                            <span id="current-date"></span>
                        </div>
                    </div>

                    <!-- Stats Summary -->
                    <div class="stats-summary">
                        <div class="summary-card fade-in-up">
                            <h3>إجمالي الزيارات</h3>
                            <p class="summary-value" id="total-visits">0</p>
                        </div>
                        <div class="summary-card fade-in-up">
                            <h3>زيارات اليوم</h3>
                            <p class="summary-value" id="daily-visits">0</p>
                            <div class="summary-change" id="daily-change">
                                <i class="fas fa-arrow-up"></i>
                                <span>0%</span>
                            </div>
                        </div>
                        <div class="summary-card fade-in-up">
                            <h3>زيارات الأسبوع</h3>
                            <p class="summary-value" id="weekly-visits">0</p>
                            <div class="summary-change" id="weekly-change">
                                <i class="fas fa-arrow-up"></i>
                                <span>0%</span>
                            </div>
                        </div>
                        <div class="summary-card fade-in-up">
                            <h3>زيارات الشهر</h3>
                            <p class="summary-value" id="monthly-visits">0</p>
                            <div class="summary-change" id="monthly-change">
                                <i class="fas fa-arrow-up"></i>
                                <span>0%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Visits Chart -->
                    <div class="dashboard-section-container">
                        <div class="section-header">
                            <h3>رسم بياني للزيارات</h3>
                            <div class="period-selector">
                                <button class="period-btn active" data-period="daily">يومي</button>
                                <button class="period-btn" data-period="weekly">أسبوعي</button>
                                <button class="period-btn" data-period="monthly">شهري</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="visits-chart"></canvas>
                        </div>
                    </div>

                    <!-- Visits Details -->
                    <div class="dashboard-section-container">
                        <div class="section-header">
                            <h3>تفاصيل الزيارات</h3>
                        </div>
                        <div class="table-container">
                            <table class="dashboard-table">
                                <thead>
                                    <tr>
                                        <th>الفترة</th>
                                        <th>عدد الزيارات</th>
                                        <th>النسبة المئوية</th>
                                        <th>التغيير</th>
                                    </tr>
                                </thead>
                                <tbody id="visits-details">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </section>

    <!-- JavaScript Files -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/visit-counter.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Display current date
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-KW', options);

            // Get visit statistics
            const stats = visitCounter.getVisitStats();

            // Update summary cards
            document.getElementById('total-visits').textContent = stats.totalVisits;
            document.getElementById('daily-visits').textContent = stats.dailyVisits;
            document.getElementById('weekly-visits').textContent = stats.weeklyVisits;
            document.getElementById('monthly-visits').textContent = stats.monthlyVisits;

            // Update change indicators
            updateChangeIndicator('daily-change', stats.dailyGrowth);
            updateChangeIndicator('weekly-change', stats.weeklyGrowth);
            updateChangeIndicator('monthly-change', stats.monthlyGrowth);

            // Initialize chart with daily data
            initChart('daily');

            // Period selector buttons
            const periodButtons = document.querySelectorAll('.period-btn');
            periodButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    periodButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Update chart
                    const period = this.getAttribute('data-period');
                    initChart(period);
                });
            });

            // Populate visits details table
            populateVisitsTable();

            // Logout functionality
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();

                // Show confirmation notification
                notifications.info('جاري تسجيل الخروج...', 2000, function() {
                    // Clear admin state
                    localStorage.removeItem('isAdmin');
                    localStorage.removeItem('current_user');

                    // Redirect to login page
                    window.location.href = '../pages/login.html';
                });
            });
        });

        /**
         * Update change indicator
         */
        function updateChangeIndicator(elementId, percentage) {
            const element = document.getElementById(elementId);
            const valueSpan = element.querySelector('span');
            const icon = element.querySelector('i');

            // Set value
            valueSpan.textContent = `${percentage}%`;

            // Set icon and class
            if (percentage > 0) {
                element.className = 'summary-change positive';
                icon.className = 'fas fa-arrow-up';
            } else if (percentage < 0) {
                element.className = 'summary-change negative';
                icon.className = 'fas fa-arrow-down';
            } else {
                element.className = 'summary-change neutral';
                icon.className = 'fas fa-minus';
            }
        }

        /**
         * Initialize chart
         */
        function initChart(period) {
            // Get chart data
            const chartData = visitCounter.getChartData(period);

            // Get canvas context
            const ctx = document.getElementById('visits-chart').getContext('2d');

            // Destroy existing chart if any
            if (window.visitsChart) {
                window.visitsChart.destroy();
            }

            // Create new chart
            window.visitsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        label: 'عدد الزيارات',
                        data: chartData.values,
                        backgroundColor: 'rgba(0, 137, 123, 0.2)',
                        borderColor: 'rgba(0, 137, 123, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgba(0, 137, 123, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleFont: {
                                family: 'Tajawal',
                                size: 14
                            },
                            bodyFont: {
                                family: 'Tajawal',
                                size: 14
                            },
                            padding: 12,
                            displayColors: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                font: {
                                    family: 'Tajawal'
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Tajawal'
                                }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        /**
         * Populate visits table
         */
        function populateVisitsTable() {
            const tableBody = document.getElementById('visits-details');
            const stats = visitCounter.getVisitStats();
            const chartData = {
                daily: visitCounter.getChartData('daily'),
                weekly: visitCounter.getChartData('weekly'),
                monthly: visitCounter.getChartData('monthly')
            };

            // Clear table
            tableBody.innerHTML = '';

            // Add daily rows
            chartData.daily.labels.forEach((label, index) => {
                const value = chartData.daily.values[index];
                const percentage = Math.round((value / stats.totalVisits) * 100) || 0;

                // Calculate change (compare with previous day if available)
                let change = 0;
                if (index > 0) {
                    const prevValue = chartData.daily.values[index - 1];
                    change = prevValue > 0 ? Math.round(((value - prevValue) / prevValue) * 100) : 0;
                }

                addTableRow(tableBody, `يوم ${label}`, value, percentage, change);
            });

            // Add weekly rows
            chartData.weekly.labels.forEach((label, index) => {
                const value = chartData.weekly.values[index];
                const percentage = Math.round((value / stats.totalVisits) * 100) || 0;

                // Calculate change (compare with previous week if available)
                let change = 0;
                if (index > 0) {
                    const prevValue = chartData.weekly.values[index - 1];
                    change = prevValue > 0 ? Math.round(((value - prevValue) / prevValue) * 100) : 0;
                }

                addTableRow(tableBody, label, value, percentage, change);
            });

            // Add monthly rows
            chartData.monthly.labels.forEach((label, index) => {
                const value = chartData.monthly.values[index];
                const percentage = Math.round((value / stats.totalVisits) * 100) || 0;

                // Calculate change (compare with previous month if available)
                let change = 0;
                if (index > 0) {
                    const prevValue = chartData.monthly.values[index - 1];
                    change = prevValue > 0 ? Math.round(((value - prevValue) / prevValue) * 100) : 0;
                }

                addTableRow(tableBody, `شهر ${label}`, value, percentage, change);
            });
        }

        /**
         * Add table row
         */
        function addTableRow(tableBody, period, visits, percentage, change) {
            const row = document.createElement('tr');

            // Create change indicator
            let changeHTML = '';
            if (change > 0) {
                changeHTML = `<span class="summary-change positive"><i class="fas fa-arrow-up"></i> ${change}%</span>`;
            } else if (change < 0) {
                changeHTML = `<span class="summary-change negative"><i class="fas fa-arrow-down"></i> ${change}%</span>`;
            } else {
                changeHTML = `<span class="summary-change neutral"><i class="fas fa-minus"></i> ${change}%</span>`;
            }

            // Set row content
            row.innerHTML = `
                <td>${period}</td>
                <td>${visits}</td>
                <td>${percentage}%</td>
                <td>${changeHTML}</td>
            `;

            // Add to table
            tableBody.appendChild(row);
        }
    </script>

    <!-- Admin Check Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is admin
            const isAdmin = localStorage.getItem('isAdmin') === 'true';
            if (!isAdmin) {
                // Redirect to login page if not admin
                alert('يجب تسجيل الدخول كمدير نظام للوصول إلى هذه الصفحة');
                window.location.href = '../pages/login.html';
            }
        });
    </script>
</body>
</html>
